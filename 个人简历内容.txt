**编程语言与核心技术**
• 扎实的Java基础，熟练掌握面向对象编程、集合框架、多线程、异常处理、IO流等核心知识

**后端框架与技术栈**
• 熟练使用Spring Boot、Spring MVC、MyBatis等主流框架，理解IOC、AOP、依赖注入等核心原理
• 具备RESTful API设计与开发经验，熟悉JSON数据交互和接口文档编写

**数据库与缓存技术**
• 熟练使用MySQL进行数据库设计与优化，掌握SQL语句编写、索引优化、事务处理
• 熟悉Redis缓存应用，了解缓存策略、数据结构及常见问题解决方案

**微服务与分布式**
• 了解Spring Cloud微服务架构，熟悉Nacos、Gateway、OpenFeign等组件使用
• 掌握分布式系统基本概念，了解服务注册发现、负载均衡、熔断限流等机制

**中间件与工具**
• 熟悉RabbitMQ消息队列、Nginx反向代理等中间件的基本使用
• 熟练使用Maven项目管理、Git版本控制、IDEA开发环境

**开发与部署**
• 具备Linux基本操作能力，了解Docker容器化部署
• 熟悉单元测试编写，具备良好的代码规范和调试能力

**前端技能**
• 掌握HTML、CSS、JavaScript基础，了解Vue.js框架，能够进行前后端分离开发